'use client';

import React from 'react';

interface DayForecast {
  day: string;
  icon: string;
  tempHigh: string;
  tempLow: string;
  humidity: string;
  evapotranspiration: string;
}

const WeatherIcon: React.FC<{ type: string }> = ({ type }) => {
  if (type === 'sunny') {
    return (
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="4" fill="#FCD34D"/>
        <path d="M12 2V4M12 20V22M4.93 4.93L6.34 6.34M17.66 17.66L19.07 19.07M2 12H4M20 12H22M6.34 17.66L4.93 19.07M19.07 4.93L17.66 6.34" stroke="#FCD34D" strokeWidth="2" strokeLinecap="round"/>
      </svg>
    );
  }
  
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
      <path d="M18 10H22C22 7.79086 20.2091 6 18 6C17.7 6 17.4 6.1 17.1 6.1C16.5 3.6 14.4 2 12 2C9.2 2 7 4.2 7 7C7 7.3 7 7.6 7.1 7.9C5.3 8.4 4 10 4 12C4 14.2 5.8 16 8 16H18C20.2 16 22 14.2 22 12" fill="#9CA3AF"/>
    </svg>
  );
};

export const WeatherForecastCard: React.FC = () => {
  const forecast: DayForecast[] = [
    {
      day: 'Today',
      icon: 'sunny',
      tempHigh: 'H 22°C',
      tempLow: 'L 20°C',
      humidity: '70%',
      evapotranspiration: 'ETo ↗ 2.8mm'
    },
    {
      day: 'Tomorrow',
      icon: 'cloudy',
      tempHigh: 'H 24°C',
      tempLow: 'L 18°C',
      humidity: '65%',
      evapotranspiration: 'ETo ↗ 3.1mm'
    },
    {
      day: 'Friday',
      icon: 'cloudy',
      tempHigh: 'H 26°C',
      tempLow: 'L 19°C',
      humidity: '68%',
      evapotranspiration: 'ETo ↗ 3.3mm'
    },
    {
      day: 'Saturday',
      icon: 'sunny',
      tempHigh: 'H 28°C',
      tempLow: 'L 21°C',
      humidity: '62%',
      evapotranspiration: 'ETo ↗ 3.5mm'
    },
    {
      day: 'Sunday',
      icon: 'cloudy',
      tempHigh: 'H 25°C',
      tempLow: 'L 20°C',
      humidity: '72%',
      evapotranspiration: 'ETo ↗ 3.0mm'
    },
    {
      day: 'Monday',
      icon: 'sunny',
      tempHigh: 'H 27°C',
      tempLow: 'L 22°C',
      humidity: '58%',
      evapotranspiration: 'ETo ↗ 3.4mm'
    },
    {
      day: 'Tuesday',
      icon: 'cloudy',
      tempHigh: 'H 23°C',
      tempLow: 'L 18°C',
      humidity: '75%',
      evapotranspiration: 'ETo ↗ 2.9mm'
    }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-lg p-4 border border-gray-100 h-full flex flex-col">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-lg font-semibold text-gray-800">Next 7 days</h3>
        <div className="flex gap-4 text-xs text-gray-500">
          <span className="font-medium">Temperature</span>
          <span className="font-medium">Humidity</span>
          <span className="font-medium">Evapotranspiration</span>
        </div>
      </div>

      <div className="space-y-2 flex-1 overflow-y-auto">
        {forecast.map((day, index) => (
          <div key={index} className="flex items-center justify-between py-1.5 border-b border-gray-100 last:border-b-0">
            <div className="flex items-center gap-2 w-20">
              <span className="text-xs font-medium text-gray-700 min-w-0">{day.day}</span>
              <WeatherIcon type={day.icon} />
            </div>

            <div className="flex items-center gap-6 flex-1 justify-end">
              <div className="flex gap-2 text-xs w-20">
                <span className="font-semibold text-gray-800">{day.tempHigh}</span>
                <span className="text-gray-500">{day.tempLow}</span>
              </div>

              <div className="w-14 text-center">
                <div className="inline-flex items-center gap-1 bg-gray-100 rounded-full px-1.5 py-0.5">
                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full"></div>
                  <span className="text-xs font-medium text-gray-700">{day.humidity}</span>
                </div>
              </div>

              <div className="w-20 text-right">
                <span className="text-xs font-medium text-gray-700">{day.evapotranspiration}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
