'use client';

import React from 'react';
import Image from "next/image";

export const FieldStatusCard: React.FC = () => {
  return (
    <div className="relative flex flex-col items-center h-80 overflow-hidden rounded-2xl">
      <Image
        src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=800&q=80"
        alt="Campo"
        fill
        className="object-cover blur-md absolute inset-0 z-0 rounded-2xl"
        style={{objectPosition: 'center'}}
      />
      <div className="absolute inset-0 bg-black bg-opacity-50 z-10 rounded-2xl" />
      <div className="relative z-20 flex w-full gap-4 p-6 flex-1">
        <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-4 flex flex-col items-center">
          <span className="mb-1 block">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g filter="url(#ph-blur)">
                <ellipse cx="18" cy="28" rx="8" ry="3" fill="#22C55E" fillOpacity="0.15"/>
              </g>
              <path d="M18 28C18 28 10 22 10 15C10 10 14 6 18 6C22 6 26 10 26 15C26 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
              <path d="M18 28C18 28 14 22 14 17C14 14 16 12 18 12C20 12 22 14 22 17C22 22 18 28 18 28Z" stroke="#22C55E" strokeWidth="2" fill="none"/>
              <defs>
                <filter id="ph-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feGaussianBlur stdDeviation="1.5"/>
                </filter>
              </defs>
            </svg>
          </span>
          <span className="text-4xl font-extrabold text-white drop-shadow">93%</span>
          <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
          <div className="text-white font-semibold text-lg drop-shadow">Plant's health</div>
        </div>
        <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-4 flex flex-col items-center">
          <span className="mb-1 block">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g filter="url(#wd-blur)">
                <ellipse cx="18" cy="28" rx="8" ry="3" fill="#38BDF8" fillOpacity="0.15"/>
              </g>
              <path d="M18 7C18 7 26 17 26 23C26 27 22.4183 31 18 31C13.5817 31 10 27 10 23C10 17 18 7 18 7Z" stroke="#BAE6FD" strokeWidth="2" fill="#38BDF8"/>
              <defs>
                <filter id="wd-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feGaussianBlur stdDeviation="1.5"/>
                </filter>
              </defs>
            </svg>
          </span>
          <span className="text-4xl font-extrabold text-white drop-shadow">85%</span>
          <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
          <div className="text-white font-semibold text-lg drop-shadow">Water depth</div>
        </div>
        <div className="flex-1 rounded-2xl bg-black/50 backdrop-blur-md shadow-xl p-4 flex flex-col items-center">
          <span className="mb-1 block">
            <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g filter="url(#soil-blur)">
                <ellipse cx="18" cy="28" rx="8" ry="3" fill="#FACC15" fillOpacity="0.15"/>
              </g>
              <circle cx="18" cy="17" r="7" stroke="#FACC15" strokeWidth="2" fill="#FDE68A"/>
              <path d="M11 17C11 17 13.5 14 18 14C22.5 14 25 17 25 17" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
              <path d="M18 10C18 10 18 24 18 24" stroke="#22C55E" strokeWidth="1.5" fill="none"/>
              <defs>
                <filter id="soil-blur" x="6" y="23" width="24" height="10" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feGaussianBlur stdDeviation="1.5"/>
                </filter>
              </defs>
            </svg>
          </span>
          <span className="text-4xl font-extrabold text-white drop-shadow">74%</span>
          <div className="text-white text-xs mb-1 drop-shadow">of reference value</div>
          <div className="text-white font-semibold text-lg drop-shadow">Soil</div>
        </div>
      </div>
      <div className="relative z-20 flex justify-between w-full text-white text-lg font-medium drop-shadow mt-6 px-6">
        <span>10 days to harvest</span>
        <span>64/74</span>
      </div>
      <div className="relative z-20 w-full flex items-center justify-center px-6 mt-3">
        <div className="w-full h-3 bg-white/20 rounded-full overflow-hidden shadow-inner">
          <div
            className="h-full bg-lime-400 rounded-full transition-all duration-500"
            style={{ width: `${(64/74)*100}%` }}
          />
        </div>
      </div>
      <div className="h-8" />
    </div>
  );
};
